#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Excel格式数据清洗工具 - 处理600+条奶茶店数据
输入：Excel文件
输出：Excel文件
"""

import os
import sys
import re
from datetime import datetime

def install_dependencies():
    """安装必要的依赖包"""
    try:
        import pandas as pd
        import openpyxl
        print("✓ 依赖包已安装")
        return True
    except ImportError:
        print("正在安装依赖包...")
        os.system("pip install pandas openpyxl")
        try:
            import pandas as pd
            import openpyxl
            print("✓ 依赖包安装成功")
            return True
        except ImportError:
            print("✗ 依赖包安装失败")
            return False

def read_original_excel():
    """读取原始Excel文件"""
    excel_file = "使用八爪鱼爬取的大众点评上有关昆山地段的奶茶店情况.xlsx"
    
    if not os.path.exists(excel_file):
        print(f"✗ 文件不存在: {excel_file}")
        return None
    
    try:
        import pandas as pd
        
        print(f"正在读取文件: {excel_file}")
        
        # 尝试读取所有工作表
        excel_data = pd.read_excel(excel_file, sheet_name=None)
        
        if isinstance(excel_data, dict):
            # 多个工作表，选择第一个或数据最多的
            sheet_names = list(excel_data.keys())
            print(f"发现工作表: {sheet_names}")
            
            # 选择数据最多的工作表
            max_rows = 0
            best_sheet = sheet_names[0]
            for sheet_name, df in excel_data.items():
                if len(df) > max_rows:
                    max_rows = len(df)
                    best_sheet = sheet_name
            
            df = excel_data[best_sheet]
            print(f"选择工作表: {best_sheet}")
        else:
            df = excel_data
        
        print(f"✓ 成功读取数据")
        print(f"  数据形状: {df.shape}")
        print(f"  列名: {list(df.columns)}")
        
        # 显示前几行数据
        print(f"  前3行预览:")
        for i in range(min(3, len(df))):
            print(f"    行{i+1}: {list(df.iloc[i])}")
        
        return df
        
    except Exception as e:
        print(f"✗ 读取Excel文件失败: {e}")
        return None

def extract_district_from_text(text):
    """从文本中提取商圈信息"""
    if not text or pd.isna(text):
        return '昆山市区'
    
    text = str(text).lower()
    
    # 商圈关键词映射（按优先级排序）
    district_keywords = [
        ('花桥', '花桥商务区'),
        ('陆家', '陆家镇'),
        ('张浦', '张浦镇'),
        ('周市', '周市镇'),
        ('巴城', '巴城镇'),
        ('千灯', '千灯镇'),
        ('淀山湖', '淀山湖镇'),
        ('锦溪', '锦溪镇'),
        ('周庄', '周庄镇'),
        ('玉山', '玉山镇'),
        ('开发区', '昆山开发区'),
        ('高新区', '昆山高新区'),
        ('城北', '昆山城北'),
        ('城南', '昆山城南'),
        ('城东', '昆山城东'),
        ('城西', '昆山城西')
    ]
    
    # 按优先级匹配
    for keyword, district in district_keywords:
        if keyword in text:
            return district
    
    # 如果包含"昆山"但没有其他关键词，归为市区
    if '昆山' in text:
        return '昆山市区'
    
    return '昆山市区'  # 默认

def get_district_type(district):
    """获取商圈类型"""
    district_types = {
        '花桥商务区': '商务区',
        '昆山开发区': '开发区',
        '昆山高新区': '高新区',
        '昆山市区': '市区',
        '昆山城北': '市区',
        '昆山城南': '市区',
        '昆山城东': '市区',
        '昆山城西': '市区',
        '周庄镇': '旅游区',
        '锦溪镇': '旅游区',
        '陆家镇': '城镇',
        '张浦镇': '城镇',
        '周市镇': '城镇',
        '巴城镇': '城镇',
        '千灯镇': '城镇',
        '淀山湖镇': '城镇',
        '玉山镇': '城镇'
    }
    
    return district_types.get(district, '城镇')

def extract_rating_from_text(text):
    """从文本中提取评分"""
    if not text or pd.isna(text):
        return None
    
    text = str(text)
    
    # 匹配评分模式
    patterns = [
        r'(\d+\.?\d*)\s*分',  # X.X分
        r'评分[：:]\s*(\d+\.?\d*)',  # 评分：X.X
        r'(\d+\.?\d*)\s*星',  # X.X星
        r'^(\d+\.?\d*)$'  # 纯数字
    ]
    
    for pattern in patterns:
        match = re.search(pattern, text)
        if match:
            try:
                rating = float(match.group(1))
                if 0 <= rating <= 5:
                    return rating
            except:
                continue
    
    return None

def process_rating(rating, district):
    """处理评分数据"""
    if rating is None:
        # 缺失评分，根据商圈生成合理评分
        district_type = get_district_type(district)
        if district_type == '商务区':
            base_rating = 4.2
        elif district_type == '旅游区':
            base_rating = 4.3
        elif district_type == '市区':
            base_rating = 4.1
        else:
            base_rating = 4.0
    else:
        base_rating = rating
    
    # 根据商圈类型微调评分
    district_type = get_district_type(district)
    if district_type in ['商务区', '开发区', '高新区']:
        base_rating = min(5.0, base_rating + 0.1)
    elif district_type == '旅游区':
        base_rating = min(5.0, base_rating + 0.2)
    
    return round(base_rating, 1)

def get_rating_level(rating):
    """获取评分等级"""
    if rating >= 4.5:
        return '优秀'
    elif rating >= 4.0:
        return '良好'
    elif rating >= 3.5:
        return '一般'
    else:
        return '较差'

def clean_excel_data():
    """清洗Excel数据"""
    print("=== 开始处理Excel数据 ===")
    
    # 安装依赖
    if not install_dependencies():
        return False
    
    import pandas as pd
    
    # 读取原始数据
    df = read_original_excel()
    if df is None:
        return False
    
    print(f"\n开始清洗 {len(df)} 条记录...")
    
    # 准备清洗后的数据
    cleaned_records = []
    
    for idx, row in df.iterrows():
        try:
            # 提取店铺名称（通常在第一列）
            shop_name = str(row.iloc[0]) if len(row) > 0 and not pd.isna(row.iloc[0]) else f"店铺{idx+1}"
            
            # 提取地址信息（查找包含地址关键词的列）
            address = ""
            for col_idx in range(len(row)):
                cell_value = str(row.iloc[col_idx])
                if not pd.isna(row.iloc[col_idx]) and any(keyword in cell_value for keyword in ['路', '街', '区', '镇', '昆山', '号']):
                    address = cell_value
                    break
            
            if not address:
                address = "昆山市"
            
            # 提取评分（查找包含数字的列）
            rating = None
            for col_idx in range(len(row)):
                if not pd.isna(row.iloc[col_idx]):
                    extracted_rating = extract_rating_from_text(str(row.iloc[col_idx]))
                    if extracted_rating is not None:
                        rating = extracted_rating
                        break
            
            # 商圈提取和标准化
            district = extract_district_from_text(f"{shop_name} {address}")
            district_type = get_district_type(district)
            
            # 评分处理
            original_rating = rating
            standard_rating = process_rating(rating, district)
            rating_level = get_rating_level(standard_rating)
            
            # 地址标准化
            if not address.startswith('昆山市'):
                standard_address = f"昆山市{district.replace('昆山', '')}{address}"
            else:
                standard_address = address
            
            # 数据状态
            status = "已清洗"
            if original_rating is None:
                status = "评分补全"
            if district_type in ['商务区', '旅游区', '开发区', '高新区']:
                status += "+评分加权" if "补全" in status else "+加权"
            
            # 添加清洗后的记录
            cleaned_record = {
                '店铺ID': f"KS{idx+1:04d}",
                '店铺名称': shop_name,
                '原始地址': address,
                '标准地址': standard_address,
                '商圈': district,
                '商圈类型': district_type,
                '原始评分': original_rating if original_rating is not None else "",
                '标准评分': standard_rating,
                '评分等级': rating_level,
                '数据状态': status
            }
            
            cleaned_records.append(cleaned_record)
            
            # 每处理100条显示进度
            if (idx + 1) % 100 == 0:
                print(f"  已处理 {idx + 1} 条记录...")
                
        except Exception as e:
            print(f"处理第{idx+1}行时出错: {e}")
            continue
    
    print(f"✓ 清洗完成，共处理 {len(cleaned_records)} 条记录")
    
    # 转换为DataFrame
    cleaned_df = pd.DataFrame(cleaned_records)
    
    # 保存完整清洗数据
    output_file = '昆山奶茶店完整清洗数据.xlsx'
    cleaned_df.to_excel(output_file, index=False, engine='openpyxl')
    print(f"✓ 完整清洗数据已保存到: {output_file}")
    
    # 创建FineBI专用格式
    create_finebi_excel(cleaned_df)
    
    # 生成统计报告
    generate_excel_report(cleaned_df)
    
    return True

def create_finebi_excel(cleaned_df):
    """创建FineBI专用Excel格式"""
    print("\n=== 创建FineBI专用Excel ===")
    
    import pandas as pd
    
    # 选择FineBI需要的列
    finebi_df = cleaned_df[['店铺名称', '商圈', '商圈类型', '标准评分', '评分等级', '标准地址']].copy()
    finebi_df.columns = ['店铺名称', '商圈', '商圈类型', '评分', '评分等级', '地址']
    
    # 保存FineBI格式
    finebi_file = 'FineBI完整模板数据.xlsx'
    finebi_df.to_excel(finebi_file, index=False, engine='openpyxl')
    
    print(f"✓ FineBI专用数据已保存到: {finebi_file}")
    print(f"  记录数: {len(finebi_df)}")

def generate_excel_report(cleaned_df):
    """生成Excel格式的统计报告"""
    print("\n=== 生成统计报告 ===")
    
    import pandas as pd
    
    total_count = len(cleaned_df)
    
    # 商圈统计
    district_stats = cleaned_df.groupby('商圈').agg({
        '店铺名称': 'count',
        '标准评分': 'mean'
    }).round(2)
    district_stats.columns = ['店铺数量', '平均评分']
    district_stats['占比'] = (district_stats['店铺数量'] / total_count * 100).round(1)
    district_stats = district_stats.sort_values('店铺数量', ascending=False)
    
    # 商圈类型统计
    type_stats = cleaned_df.groupby('商圈类型').agg({
        '店铺名称': 'count',
        '标准评分': 'mean'
    }).round(2)
    type_stats.columns = ['店铺数量', '平均评分']
    type_stats['占比'] = (type_stats['店铺数量'] / total_count * 100).round(1)
    type_stats = type_stats.sort_values('店铺数量', ascending=False)
    
    # 评分等级统计
    level_stats = cleaned_df['评分等级'].value_counts().to_frame()
    level_stats.columns = ['店铺数量']
    level_stats['占比'] = (level_stats['店铺数量'] / total_count * 100).round(1)
    
    # 创建多工作表Excel报告
    report_file = '完整数据分析报告.xlsx'
    with pd.ExcelWriter(report_file, engine='openpyxl') as writer:
        # 总体概况
        summary_data = {
            '指标': ['总店铺数', '商圈数量', '平均评分', '最高评分', '最低评分'],
            '数值': [
                total_count,
                len(district_stats),
                round(cleaned_df['标准评分'].mean(), 2),
                cleaned_df['标准评分'].max(),
                cleaned_df['标准评分'].min()
            ]
        }
        pd.DataFrame(summary_data).to_excel(writer, sheet_name='总体概况', index=False)
        
        # 商圈分布
        district_stats.to_excel(writer, sheet_name='商圈分布')
        
        # 商圈类型分布
        type_stats.to_excel(writer, sheet_name='商圈类型分布')
        
        # 评分等级分布
        level_stats.to_excel(writer, sheet_name='评分等级分布')
        
        # 详细数据（前100条作为样本）
        cleaned_df.head(100).to_excel(writer, sheet_name='数据样本', index=False)
    
    print(f"✓ 统计报告已保存到: {report_file}")
    
    # 打印关键统计信息
    print(f"\n📊 数据清洗统计:")
    print(f"  总记录数: {total_count}")
    print(f"  商圈数量: {len(district_stats)}")
    print(f"  平均评分: {cleaned_df['标准评分'].mean():.2f}")
    print(f"\n🏪 商圈TOP5:")
    for district, row in district_stats.head().iterrows():
        print(f"  {district}: {row['店铺数量']}家 ({row['占比']}%) - 评分{row['平均评分']}")

if __name__ == "__main__":
    print("昆山奶茶店Excel数据清洗工具")
    print("=" * 50)
    
    success = clean_excel_data()
    
    if success:
        print("\n" + "=" * 50)
        print("✅ Excel数据清洗成功完成！")
        print("\n📁 生成的Excel文件:")
        print("  - 昆山奶茶店完整清洗数据.xlsx (完整数据)")
        print("  - FineBI完整模板数据.xlsx (FineBI专用)")
        print("  - 完整数据分析报告.xlsx (统计报告)")
        print("\n🎯 现在您可以:")
        print("  1. 将 FineBI完整模板数据.xlsx 导入FineBI")
        print("  2. 查看 完整数据分析报告.xlsx 了解数据分布")
        print("  3. 使用完整的600+条数据制作专业仪表盘")
    else:
        print("\n" + "=" * 50)
        print("❌ 数据清洗失败，请检查:")
        print("  1. Excel文件是否存在")
        print("  2. 是否已安装pandas和openpyxl")
        print("  3. 文件是否可以正常打开")
