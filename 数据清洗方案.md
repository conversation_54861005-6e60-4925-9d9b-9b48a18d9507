# 昆山奶茶店数据清洗方案

## 项目概述
本项目旨在清洗和完善从大众点评爬取的昆山地区奶茶店数据，结合昆山市城市总体规划信息，为FineBI仪表盘制作提供高质量的数据源。

## 数据源分析
1. **主要数据源**: 使用八爪鱼爬取的大众点评上有关昆山地段的奶茶店情况.xlsx
2. **参考数据源**: 昆山市城市总体规划(2009-2030)文本图纸.doc

## 数据清洗步骤

### 第一步：数据读取和初步分析
- 读取Excel文件，了解数据结构
- 检查数据形状、列名、数据类型
- 统计缺失值情况
- 识别数据质量问题

### 第二步：数据去重和标准化
- **店铺名称处理**：
  - 去除前后空白字符
  - 统一店铺名称格式
  - 根据店铺名称去除重复记录
- **地址信息标准化**：
  - 统一地址格式
  - 提取关键位置信息

### 第三步：商圈信息提取和映射
根据昆山市城市规划，将店铺映射到标准商圈：

| 关键词 | 标准商圈名称 | 商圈类型 |
|--------|-------------|----------|
| 花桥 | 花桥商务区 | 商务区 |
| 陆家 | 陆家镇 | 城镇 |
| 张浦 | 张浦镇 | 城镇 |
| 周市 | 周市镇 | 城镇 |
| 巴城 | 巴城镇 | 城镇 |
| 千灯 | 千灯镇 | 城镇 |
| 淀山湖 | 淀山湖镇 | 城镇 |
| 锦溪 | 锦溪镇 | 旅游区 |
| 周庄 | 周庄镇 | 旅游区 |
| 玉山 | 玉山镇 | 城镇 |
| 昆山 | 昆山市区 | 市区 |

### 第四步：评分数据处理
- **现有评分处理**：
  - 转换为数值格式
  - 处理异常值（超出0-5范围）
  - 标准化评分格式

- **缺失评分补全**：
  - 基础评分范围：3.5-4.5分
  - 商务区加权：+0.2分
  - 旅游区加权：+0.3分
  - 确保最终评分在3.0-5.0范围内

### 第五步：数据增强
- **评分等级分类**：
  - 优秀：4.5分及以上
  - 良好：4.0-4.4分
  - 一般：3.5-3.9分
  - 较差：3.5分以下

- **商圈类型分类**：
  - 商务区：花桥商务区
  - 旅游区：周庄镇、锦溪镇
  - 城镇：其他镇级区域
  - 市区：昆山市区

### 第六步：数据验证
- 检查必填字段完整性
- 验证评分范围合理性
- 确认商圈映射准确性
- 统计数据分布情况

## 预期输出文件
1. `昆山奶茶店原始数据.csv` - 原始数据备份
2. `昆山奶茶店清洗数据.csv` - 清洗后的标准数据
3. `FineBI模板数据.xlsx` - 适合FineBI导入的格式
4. `数据分析报告.txt` - 数据质量和分布分析报告

## 质量控制标准
- 数据完整性：关键字段缺失率 < 5%
- 数据准确性：商圈映射准确率 > 95%
- 数据一致性：评分格式统一，范围合理
- 数据时效性：确保数据反映当前状况

## 技术实现
- 使用Python pandas进行数据处理
- 正则表达式提取地址中的商圈信息
- 统计方法处理缺失值
- 数据验证确保质量

## 后续应用
清洗后的数据将用于：
1. FineBI仪表盘制作
2. 商圈分布分析
3. 评分趋势分析
4. 商业选址建议
