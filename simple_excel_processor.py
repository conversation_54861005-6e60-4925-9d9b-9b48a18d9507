#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化版Excel处理器 - 直接处理600+条数据
"""

def main():
    print("昆山奶茶店Excel数据处理器")
    print("=" * 40)
    
    try:
        import pandas as pd
        print("✓ pandas可用")
    except ImportError:
        print("正在安装pandas...")
        import subprocess
        subprocess.run(["pip", "install", "pandas", "openpyxl"])
        import pandas as pd
        print("✓ pandas安装完成")
    
    # 读取原始Excel文件
    input_file = "使用八爪鱼爬取的大众点评上有关昆山地段的奶茶店情况.xlsx"
    
    try:
        print(f"正在读取: {input_file}")
        df = pd.read_excel(input_file)
        print(f"✓ 读取成功，共 {len(df)} 条记录")
        print(f"列名: {list(df.columns)}")
        
        # 显示前几行
        print("\n前3行数据:")
        for i in range(min(3, len(df))):
            print(f"行{i+1}: {list(df.iloc[i])}")
        
    except Exception as e:
        print(f"✗ 读取失败: {e}")
        return
    
    # 数据清洗
    print(f"\n开始清洗 {len(df)} 条数据...")
    
    cleaned_data = []
    
    # 商圈映射
    district_map = {
        '花桥': '花桥商务区', '陆家': '陆家镇', '张浦': '张浦镇',
        '周市': '周市镇', '巴城': '巴城镇', '千灯': '千灯镇',
        '锦溪': '锦溪镇', '周庄': '周庄镇', '玉山': '玉山镇'
    }
    
    type_map = {
        '花桥商务区': '商务区', '昆山市区': '市区',
        '周庄镇': '旅游区', '锦溪镇': '旅游区'
    }
    
    for idx, row in df.iterrows():
        try:
            # 提取店铺名称
            name = str(row.iloc[0]) if not pd.isna(row.iloc[0]) else f"店铺{idx+1}"
            
            # 提取地址
            address = ""
            for col in range(len(row)):
                val = str(row.iloc[col])
                if any(k in val for k in ['路', '街', '区', '镇', '昆山']):
                    address = val
                    break
            if not address:
                address = "昆山市"
            
            # 提取评分
            rating = 4.0  # 默认评分
            for col in range(len(row)):
                val = str(row.iloc[col])
                if any(c.isdigit() for c in val):
                    import re
                    numbers = re.findall(r'\d+\.?\d*', val)
                    if numbers:
                        try:
                            r = float(numbers[0])
                            if 0 <= r <= 5:
                                rating = r
                                break
                        except:
                            continue
            
            # 确定商圈
            district = '昆山市区'
            for key, value in district_map.items():
                if key in name or key in address:
                    district = value
                    break
            
            # 商圈类型
            district_type = type_map.get(district, '城镇')
            
            # 评分等级
            if rating >= 4.5:
                level = '优秀'
            elif rating >= 4.0:
                level = '良好'
            elif rating >= 3.5:
                level = '一般'
            else:
                level = '较差'
            
            # 标准地址
            std_address = f"昆山市{district.replace('昆山', '')}{address}" if not address.startswith('昆山') else address
            
            cleaned_data.append({
                '店铺ID': f"KS{idx+1:04d}",
                '店铺名称': name,
                '商圈': district,
                '商圈类型': district_type,
                '评分': rating,
                '评分等级': level,
                '地址': std_address
            })
            
            if (idx + 1) % 100 == 0:
                print(f"已处理 {idx + 1} 条...")
                
        except Exception as e:
            print(f"处理第{idx+1}行出错: {e}")
            continue
    
    print(f"✓ 清洗完成，共 {len(cleaned_data)} 条记录")
    
    # 转换为DataFrame
    result_df = pd.DataFrame(cleaned_data)
    
    # 保存Excel文件
    output_files = [
        '昆山奶茶店完整清洗数据.xlsx',
        'FineBI完整模板数据.xlsx'
    ]
    
    for output_file in output_files:
        result_df.to_excel(output_file, index=False)
        print(f"✓ 已保存: {output_file}")
    
    # 统计信息
    print(f"\n📊 数据统计:")
    print(f"总记录数: {len(result_df)}")
    
    district_stats = result_df['商圈'].value_counts()
    print(f"\n商圈分布:")
    for district, count in district_stats.head(10).items():
        pct = count / len(result_df) * 100
        print(f"  {district}: {count}家 ({pct:.1f}%)")
    
    type_stats = result_df['商圈类型'].value_counts()
    print(f"\n商圈类型:")
    for dtype, count in type_stats.items():
        pct = count / len(result_df) * 100
        print(f"  {dtype}: {count}家 ({pct:.1f}%)")
    
    level_stats = result_df['评分等级'].value_counts()
    print(f"\n评分等级:")
    for level, count in level_stats.items():
        pct = count / len(result_df) * 100
        print(f"  {level}: {count}家 ({pct:.1f}%)")
    
    print(f"\n平均评分: {result_df['评分'].mean():.2f}")
    
    # 创建统计报告Excel
    with pd.ExcelWriter('完整数据统计报告.xlsx') as writer:
        result_df.to_excel(writer, sheet_name='清洗数据', index=False)
        district_stats.to_excel(writer, sheet_name='商圈统计')
        type_stats.to_excel(writer, sheet_name='类型统计')
        level_stats.to_excel(writer, sheet_name='评分统计')
    
    print("✓ 统计报告已保存: 完整数据统计报告.xlsx")
    
    print(f"\n🎉 处理完成！现在您有 {len(result_df)} 条完整数据可用于FineBI分析")

if __name__ == "__main__":
    main()
