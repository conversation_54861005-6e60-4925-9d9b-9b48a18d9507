#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
将CSV数据转换为Excel格式
"""

import csv
import pandas as pd
from collections import Counter

def convert_csv_to_excel():
    """将CSV转换为Excel格式"""
    print("正在转换CSV数据为Excel格式...")
    
    try:
        # 读取CSV数据
        df = pd.read_csv('昆山奶茶店完整清洗数据.csv', encoding='utf-8-sig')
        
        print(f"✓ 读取CSV数据成功，共 {len(df)} 条记录")
        
        # 1. 保存完整Excel数据
        df.to_excel('昆山奶茶店完整清洗数据.xlsx', index=False, engine='openpyxl')
        print(f"✓ 完整Excel数据已保存: 昆山奶茶店完整清洗数据.xlsx ({len(df)}条)")
        
        # 2. 创建FineBI专用Excel格式
        finebi_df = df[['店铺名称', '商圈', '商圈类型', '评分', '评分等级', '地址']].copy()
        finebi_df.to_excel('FineBI完整模板数据.xlsx', index=False, engine='openpyxl')
        print(f"✓ FineBI Excel数据已保存: FineBI完整模板数据.xlsx ({len(finebi_df)}条)")
        
        # 3. 生成统计报告Excel
        create_statistics_excel(df)
        
        return True
        
    except Exception as e:
        print(f"✗ 转换失败: {e}")
        return False

def create_statistics_excel(df):
    """创建统计报告Excel"""
    print("正在生成统计报告Excel...")
    
    total_count = len(df)
    
    # 商圈统计
    district_stats = df.groupby('商圈').agg({
        '店铺名称': 'count',
        '评分': 'mean'
    }).round(2)
    district_stats.columns = ['店铺数量', '平均评分']
    district_stats['占比(%)'] = (district_stats['店铺数量'] / total_count * 100).round(1)
    district_stats = district_stats.sort_values('店铺数量', ascending=False)
    
    # 商圈类型统计
    type_stats = df.groupby('商圈类型').agg({
        '店铺名称': 'count',
        '评分': 'mean'
    }).round(2)
    type_stats.columns = ['店铺数量', '平均评分']
    type_stats['占比(%)'] = (type_stats['店铺数量'] / total_count * 100).round(1)
    type_stats = type_stats.sort_values('店铺数量', ascending=False)
    
    # 评分等级统计
    level_stats = df['评分等级'].value_counts().to_frame()
    level_stats.columns = ['店铺数量']
    level_stats['占比(%)'] = (level_stats['店铺数量'] / total_count * 100).round(1)
    
    # 品牌统计
    df['品牌'] = df['店铺名称'].str.extract(r'([^(]+)')
    brand_stats = df['品牌'].value_counts().head(20).to_frame()
    brand_stats.columns = ['店铺数量']
    brand_stats['占比(%)'] = (brand_stats['店铺数量'] / total_count * 100).round(1)
    
    # 创建多工作表Excel
    with pd.ExcelWriter('完整数据统计报告.xlsx', engine='openpyxl') as writer:
        # 总体概况
        summary_data = {
            '指标': ['总店铺数', '商圈数量', '平均评分', '最高评分', '最低评分'],
            '数值': [
                total_count,
                df['商圈'].nunique(),
                round(df['评分'].mean(), 2),
                df['评分'].max(),
                df['评分'].min()
            ]
        }
        pd.DataFrame(summary_data).to_excel(writer, sheet_name='总体概况', index=False)
        
        # 商圈分布
        district_stats.to_excel(writer, sheet_name='商圈分布')
        
        # 商圈类型分布
        type_stats.to_excel(writer, sheet_name='商圈类型分布')
        
        # 评分等级分布
        level_stats.to_excel(writer, sheet_name='评分等级分布')
        
        # 品牌TOP20
        brand_stats.to_excel(writer, sheet_name='品牌TOP20')
        
        # 数据样本（前100条）
        df.head(100).to_excel(writer, sheet_name='数据样本', index=False)
    
    print("✓ 统计报告Excel已保存: 完整数据统计报告.xlsx")

def print_summary():
    """打印数据摘要"""
    try:
        df = pd.read_csv('昆山奶茶店完整清洗数据.csv', encoding='utf-8-sig')
        
        print("\n" + "="*60)
        print("🎉 昆山奶茶店数据处理完成！")
        print("="*60)
        
        total_count = len(df)
        print(f"📊 数据概况:")
        print(f"  总记录数: {total_count} 条")
        print(f"  商圈数量: {df['商圈'].nunique()} 个")
        print(f"  平均评分: {df['评分'].mean():.2f} 分")
        
        # 商圈分布TOP10
        print(f"\n🏪 商圈分布TOP10:")
        district_counts = df['商圈'].value_counts()
        for i, (district, count) in enumerate(district_counts.head(10).items(), 1):
            pct = count / total_count * 100
            avg_rating = df[df['商圈'] == district]['评分'].mean()
            print(f"  {i:2d}. {district}: {count}家 ({pct:.1f}%) - 平均{avg_rating:.1f}分")
        
        # 商圈类型
        print(f"\n🏢 商圈类型分布:")
        type_counts = df['商圈类型'].value_counts()
        for dtype, count in type_counts.items():
            pct = count / total_count * 100
            avg_rating = df[df['商圈类型'] == dtype]['评分'].mean()
            print(f"  {dtype}: {count}家 ({pct:.1f}%) - 平均{avg_rating:.1f}分")
        
        # 评分分布
        print(f"\n⭐ 评分等级分布:")
        level_counts = df['评分等级'].value_counts()
        for level, count in level_counts.items():
            pct = count / total_count * 100
            print(f"  {level}: {count}家 ({pct:.1f}%)")
        
        # 品牌TOP10
        df['品牌'] = df['店铺名称'].str.extract(r'([^(]+)')
        brand_counts = df['品牌'].value_counts()
        print(f"\n🏷️ 品牌TOP10:")
        for i, (brand, count) in enumerate(brand_counts.head(10).items(), 1):
            pct = count / total_count * 100
            print(f"  {i:2d}. {brand}: {count}家 ({pct:.1f}%)")
        
        print(f"\n📁 生成的文件:")
        print(f"  - 昆山奶茶店完整清洗数据.csv ({total_count}条)")
        print(f"  - 昆山奶茶店完整清洗数据.xlsx ({total_count}条)")
        print(f"  - FineBI完整模板数据.xlsx ({total_count}条)")
        print(f"  - 完整数据统计报告.xlsx (多工作表)")
        
        print(f"\n🎯 FineBI使用建议:")
        print(f"  1. 导入文件: FineBI完整模板数据.xlsx")
        print(f"  2. 主要维度: 商圈、商圈类型、评分等级")
        print(f"  3. 主要指标: 店铺数量、平均评分")
        print(f"  4. 推荐图表: 饼图(商圈分布)、柱状图(数量对比)、雷达图(评分对比)")
        
    except Exception as e:
        print(f"✗ 读取数据失败: {e}")

if __name__ == "__main__":
    print("昆山奶茶店数据Excel转换工具")
    print("="*40)
    
    if convert_csv_to_excel():
        print_summary()
    else:
        print("转换失败，请检查CSV文件是否存在")
