#!/usr/bin/env python
# -*- coding: utf-8 -*-

import csv
import os
import json
from datetime import datetime

def analyze_files():
    """分析文件并生成报告"""
    print("=== 昆山奶茶店数据分析项目 ===")
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 检查文件是否存在
    excel_file = "使用八爪鱼爬取的大众点评上有关昆山地段的奶茶店情况.xlsx"
    word_file = "昆山市城市总体规划(2009-2030)文本图纸.doc"
    
    print("文件检查:")
    print(f"  Excel文件: {'存在' if os.path.exists(excel_file) else '不存在'}")
    print(f"  Word文档: {'存在' if os.path.exists(word_file) else '不存在'}")
    print()
    
    # 生成数据清洗和分析方案
    generate_cleaning_plan()
    
    # 生成FineBI仪表盘设计方案
    generate_finebi_plan()
    
    # 生成项目文档
    generate_project_documentation()

def generate_cleaning_plan():
    """生成数据清洗方案"""
    print("=== 数据清洗方案 ===")
    
    cleaning_plan = {
        "数据源": "大众点评昆山奶茶店数据",
        "清洗步骤": [
            {
                "步骤1": "数据去重",
                "说明": "根据店铺名称去除重复记录",
                "预期效果": "确保每个店铺只有一条记录"
            },
            {
                "步骤2": "商圈信息标准化", 
                "说明": "从店铺名称或地址中提取商圈信息",
                "商圈映射": {
                    "花桥": "花桥商务区",
                    "陆家": "陆家镇",
                    "张浦": "张浦镇", 
                    "周市": "周市镇",
                    "巴城": "巴城镇",
                    "千灯": "千灯镇",
                    "淀山湖": "淀山湖镇",
                    "锦溪": "锦溪镇",
                    "周庄": "周庄镇",
                    "玉山": "玉山镇",
                    "昆山": "昆山市区"
                }
            },
            {
                "步骤3": "评分数据处理",
                "说明": "处理缺失评分，为无评分店铺生成合理评分",
                "评分规则": {
                    "商务区": "基础评分+0.2",
                    "旅游区": "基础评分+0.3", 
                    "其他区域": "基础评分3.5-4.5"
                }
            },
            {
                "步骤4": "数据验证",
                "说明": "检查数据完整性和合理性",
                "验证项": ["评分范围0-5", "商圈信息完整", "店铺名称非空"]
            }
        ]
    }
    
    # 保存清洗方案
    with open('数据清洗方案.json', 'w', encoding='utf-8') as f:
        json.dump(cleaning_plan, f, ensure_ascii=False, indent=2)
    
    print("数据清洗方案已生成并保存到 数据清洗方案.json")
    
    for step in cleaning_plan["清洗步骤"]:
        for key, value in step.items():
            if key.startswith("步骤"):
                print(f"  {key}: {value}")
            elif key == "说明":
                print(f"    说明: {value}")
    print()

def generate_finebi_plan():
    """生成FineBI仪表盘设计方案"""
    print("=== FineBI仪表盘设计方案 ===")
    
    dashboard_plan = {
        "仪表盘名称": "昆山奶茶店分布分析仪表盘",
        "数据源": "清洗后的奶茶店数据",
        "图表设计": [
            {
                "图表1": "商圈分布饼图",
                "类型": "饼图",
                "维度": "商圈",
                "指标": "店铺数量",
                "用途": "展示各商圈奶茶店分布比例"
            },
            {
                "图表2": "商圈店铺数量对比",
                "类型": "柱状图",
                "维度": "商圈",
                "指标": "店铺数量",
                "用途": "对比各商圈奶茶店数量"
            },
            {
                "图表3": "评分分布分析",
                "类型": "直方图",
                "维度": "评分区间",
                "指标": "店铺数量",
                "用途": "分析奶茶店评分分布情况"
            },
            {
                "图表4": "商圈平均评分对比",
                "类型": "雷达图",
                "维度": "商圈",
                "指标": "平均评分",
                "用途": "对比各商圈奶茶店平均评分"
            },
            {
                "图表5": "综合指标仪表盘",
                "类型": "仪表盘",
                "指标": ["总店铺数", "平均评分", "最高评分商圈"],
                "用途": "展示关键指标"
            }
        ],
        "筛选器": [
            "商圈筛选器",
            "评分范围筛选器",
            "商圈类型筛选器"
        ],
        "布局建议": {
            "顶部": "关键指标卡片",
            "左侧": "筛选器面板",
            "中央": "主要图表区域",
            "右侧": "详细信息面板"
        }
    }
    
    # 保存仪表盘方案
    with open('FineBI仪表盘设计方案.json', 'w', encoding='utf-8') as f:
        json.dump(dashboard_plan, f, ensure_ascii=False, indent=2)
    
    print("FineBI仪表盘设计方案已生成并保存到 FineBI仪表盘设计方案.json")
    
    for chart in dashboard_plan["图表设计"]:
        for key, value in chart.items():
            if key.startswith("图表"):
                print(f"  {key}: {value}")
            elif key == "用途":
                print(f"    用途: {value}")
    print()

def generate_project_documentation():
    """生成项目文档"""
    print("=== 生成项目文档 ===")
    
    doc_content = f"""
昆山奶茶店数据分析项目文档
================================

项目概述
--------
本项目旨在分析昆山地区奶茶店的分布情况，结合城市规划信息，
为商业决策提供数据支持。

数据来源
--------
1. 大众点评昆山奶茶店数据（八爪鱼爬取）
2. 昆山市城市总体规划(2009-2030)文档

项目目标
--------
1. 清洗和标准化奶茶店数据
2. 分析奶茶店在各商圈的分布情况
3. 评估不同商圈奶茶店的评分水平
4. 制作FineBI可视化仪表盘

数据处理流程
-----------
1. 数据读取和初步分析
2. 数据清洗和去重
3. 商圈信息提取和标准化
4. 评分数据处理和补全
5. 数据验证和质量检查
6. 生成FineBI适用的数据格式

预期成果
--------
1. 清洗后的标准化数据文件
2. 数据分析报告
3. FineBI仪表盘模板
4. 项目文档和使用说明

技术栈
------
- Python: 数据处理和分析
- Pandas: 数据操作
- FineBI: 数据可视化
- Excel/CSV: 数据存储格式

项目时间线
----------
- 数据清洗: 1天
- 数据分析: 1天  
- 仪表盘制作: 2天
- 文档整理: 1天

联系信息
--------
项目负责人: [待填写]
创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    # 保存项目文档
    with open('项目文档.md', 'w', encoding='utf-8') as f:
        f.write(doc_content)
    
    print("项目文档已生成并保存到 项目文档.md")
    print()

def create_sample_data():
    """创建示例数据用于测试"""
    print("=== 创建示例数据 ===")
    
    sample_data = [
        ["店铺名称", "地址", "评分", "商圈", "评分等级", "商圈类型"],
        ["蜜雪冰城(花桥店)", "花桥镇绿地大道", "4.2", "花桥商务区", "良好", "商务区"],
        ["喜茶(昆山店)", "昆山市前进路", "4.6", "昆山市区", "优秀", "市区"],
        ["一点点(陆家店)", "陆家镇陆丰路", "4.1", "陆家镇", "良好", "城镇"],
        ["CoCo都可(周庄店)", "周庄镇全福路", "4.3", "周庄镇", "良好", "旅游区"],
        ["茶百道(张浦店)", "张浦镇振新路", "4.0", "张浦镇", "良好", "城镇"],
        ["书亦烧仙草(千灯店)", "千灯镇炎武路", "3.9", "千灯镇", "一般", "城镇"],
        ["古茗(巴城店)", "巴城镇东阳路", "4.1", "巴城镇", "良好", "城镇"],
        ["益禾堂(周市店)", "周市镇青阳路", "4.0", "周市镇", "良好", "城镇"],
        ["奈雪的茶(花桥店)", "花桥镇花安路", "4.5", "花桥商务区", "优秀", "商务区"],
        ["瑞幸咖啡(锦溪店)", "锦溪镇邵甸港路", "4.2", "锦溪镇", "良好", "旅游区"]
    ]
    
    # 保存示例数据
    with open('示例数据.csv', 'w', newline='', encoding='utf-8-sig') as f:
        writer = csv.writer(f)
        writer.writerows(sample_data)
    
    print("示例数据已创建并保存到 示例数据.csv")
    print("可以使用此示例数据测试FineBI仪表盘")
    print()

if __name__ == "__main__":
    analyze_files()
    create_sample_data()
    
    print("=== 项目分析完成 ===")
    print("生成的文件:")
    print("  - 数据清洗方案.json")
    print("  - FineBI仪表盘设计方案.json") 
    print("  - 项目文档.md")
    print("  - 示例数据.csv")
    print()
    print("下一步操作:")
    print("1. 安装Python依赖: pip install pandas openpyxl python-docx")
    print("2. 运行完整分析: python read_files.py")
    print("3. 将生成的数据导入FineBI制作仪表盘")
